import { useState, useEffect, useMemo, useCallback, useRef, memo } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useAlert } from "@/contexts/AlertContext";
import { useTabState } from "@/components/TabBar";
import { useToast } from "@/hooks/use-toast";
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks";
import {
  initializeContainer,
  setInitialized,
  setJobDetails,
  setTrajectory,
  setTabContainer,
  setActiveContainerForTab,
} from "@/store/containerSlice";
import { Virtuoso } from "react-virtuoso";
import { motion, AnimatePresence } from "framer-motion";

import ErrorBoundary from "@/components/ErrorBoundary";
// @ts-ignore
import { useChat } from "ai/react";
import { ChatInput } from "@/components/ChatInput";
import "@/styles/scrollbar.css";
import { usePollingWorker } from "@/hooks/usePollingWorker";
import { DiffModal } from "@/components/DiffModal";
import { MessageItem } from "@/components/MessageItem";
import { ChatHeader } from "@/components/ChatHeader";
import {
  UIMessage,
  AgentMessage as ImportedAgentMessage,
} from "@/types/message";
import { SubagentMessagesPanel } from "@/components/sidebar/SubagentMessagesPanel";
import { AddTokenModal } from "@/components/modals/AddTokenModal";
import cn from "classnames";
import { agentApi } from "@/services/agentApi";
import { ChatInfoPanel } from "@/components/sidebar/ChatInfoPanel";
import { VSCodeLinkModal } from "@/components/modals/VSCodeLinkModal";
import { QRCodeModal } from "@/components/modals/QRCodeModal";
import { useVSCode } from "@/hooks/useVSCode";
import { usePreviewUrls } from "@/hooks/usePreviewUrls";
import { useDiff } from "@/hooks/useDiff";
import { ResponseImageData } from "@/types/message";
import { PendingArtifact } from "@/types/artifact";
import { useTrajectoryProcessor } from "@/hooks/useTrajectoryProcessor";
import { RollbackModal } from "@/components/modals/RollbackModal/components/RollbackModal"; // Re-added RollbackModal import
import RollbackFailed from "./RollbackFailed";
import { useErrorToast } from "@/components/ui/ErrorToast";
import { usePublish } from "@/hooks/usePublish";
import {
  captureError,
  isInputDisabled,
  isMobilePreviewEnabled,
  trackChatInteraction,
  trackFeatureUsage,
  useIsUploadAssetEnabled,
} from "@/services/postHogService";
import { UrlPreviewPanel } from "@/components/sidebar/UIPreviewPanel";
import { useSidePanelState } from "@/hooks/useSidePanel";
import { AgentMessageItem } from "@/components/AgentMessageItem";
import { Chunk } from "@/types/chunk";
import StableTypingAnimation from "@/components/StableTypingAnimation";
import "@/components/animations.css";
import { AgentSleepNotification } from "../../components/AgentSleepNotification";
import { PreviewPrompt } from "@/components/PreviewPrompt";

import { Spinner } from "@/components/ui/spinner";
import { useBudget } from "@/hooks/useBudget";
import { WakeupProgress } from "@/components/WakeupProgress";
import { DeployPanel } from "@/components/sidebar/DeployPanel";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import surveyService from "@/services/surveyService";
import {
  ResizablePanelGroup,
  ResizablePanel,
  AnimatedResizablePanel,
  AnimatedResizableHandle,
} from "@/components/ui/resizable";
import { removePendingMessage, selectPendingArtifacts, clearPendingArtifacts, addPendingArtifacts } from "@/store/tabSlice";
import { CUSTOM_TEXT } from "@/constants/constants";
// @ts-ignore
import animatedSpinner from "@/assets/animated-spinner.gif";
// @ts-ignore
import MatrixCodeGIF from "@/assets/fork/matrix_code.gif";
import ForkBar from "@/components/fork/ForkBar";
import { useGetConfigQuery, useLazyGetJobQuery } from "@/store/api/apiSlice";
import TaskSetupLoader from "./TaskSetup";
import { ForkModal } from "@/components/modals/ForkModal";
import { UploadAssetsModal } from "@/components/modals/UploadAssetsModal";
import { useCredits } from "@/contexts";

import SendIcon from "@/assets/send.svg";
import useScreenSize from "@/hooks/useScreenSize";

interface Message extends UIMessage {
  timestamp?: string;
  status?: "pending" | "confirmed";
  subagent_trajectory?: ImportedAgentMessage[];
  agent_name?: string;
  containerId?: string;
  expertise_type?: string;
  request_id?: string;
  base64_image_list?: ResponseImageData[];
  function_name?: string;
  action?: string;
  observation?: string;
  args?: any;
  warning_for_token_limit?: boolean;
  current_token_count?: number;
  max_token_count?: number;
  error_ts?: string;
  error?: boolean;
  error_message?: string;
  forked_job_id?: string;
  fork_status?: "running" | "success" | "failed" | null;
}

interface AgentState {
  agent_running: boolean;
  job_running: boolean;
  agent_status_new?: "running" | "paused" | "stopped" | "archived";
}

interface ChatScreenProps {
  tabId: string;
}



// Define interface for AgentFooter props
interface AgentFooterProps {
  text: string;
  rollbackStatus?: string;
  agentStatus?: boolean;
  isSubagentChunk?: boolean;
  currentChunk?: Chunk | null;
}

const AgentFooter = memo<AgentFooterProps>(
  ({ text, rollbackStatus, agentStatus, isSubagentChunk, currentChunk }) => {
    return (
      <div className="mt-4">
        {agentStatus && !isSubagentChunk && (
          <>
            <StableTypingAnimation
              initialText={text}
              currentChunk={currentChunk}
            />
            <div className="h-32" />
          </>
        )}
        {rollbackStatus === "FAILED" ? <RollbackFailed /> : null}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if any of these props change
    return (
      prevProps.text === nextProps.text &&
      prevProps.rollbackStatus === nextProps.rollbackStatus &&
      prevProps.agentStatus === nextProps.agentStatus
    );
  }
);

export function ChatScreen({ tabId }: ChatScreenProps) {

  // Hooks
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const {
    getTabState,
    updateTabState,
    clearPendingMessage,
    setPendingMessage,
    removeTab,
    activeTab,
    setTabs,
    setActiveTab,
    getTabByJobId,
  } = useTabState();

  
  const tabState = getTabState(tabId);
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const { session, user } = useAuth();
  const {
    showLogsPanel,
    showSubagentPanel,
    showUrlPreviewPanel,
    showInfoPanel,
    showDeployPanel,
    panelState,
    togglePanel,
  } = useSidePanelState();

  const {credits} = useCredits();

  const virtuosoRef = useRef<any>(null);

  // State
  const [podIsPaused, setPodIsPaused] = useState(false);
  const [archivedPod, setArchivedPod] = useState(false);

  const [githubDetails, setGithubDetails] = useState<{
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null>(null);

  const [failedPendingValue, setFailedPendingValue] = useState<string | null>(
    null
  );

  const [targetRepo, setTargetRepo] = useState<{
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null>(null);

  const [createdBefore25April, setCreatedBefore25April] = useState(false);

  const [isSubagentActive, setIsSubagentActive] = useState(false);

  const [isJobDetailsLoading, setIsJobDetailsLoading] = useState(true);

  const [selectedSubagentMessages, setSelectedSubagentMessages] = useState<
    ImportedAgentMessage["subagent_trajectory"]
  >([]);
  const [selectedSubagent, setSelectedSubagent] = useState<
    string | undefined
  >();
  const [selectedMessageId, setSelectedMessageId] = useState<
    string | undefined
  >();
  const [selectedStepNum, setSelectedStepNum] = useState<number | undefined>();
  const [isAddTokenModalOpen, setIsAddTokenModalOpen] = useState(false);
  const [addTokenVariant, setAddTokenVariant] = useState<
    "increase_budget" | "add_credits"
  >("increase_budget");
  const [addTokenTriggeredLocation, setAddTokenTriggeredLocation] = useState<
    "default" | "exit_cost" | "add_credits"
  >("default");
  const [text, setText] = useState("");

  const [modalOpen, setModalOpen] = useState<{
    fork: boolean;
  }>({
    fork: false,
  });

  const [showUploadAssetsModal, setShowUploadAssetsModal] = useState(false);
  const [uploadAssetsInitialFiles, setUploadAssetsInitialFiles] = useState<File[]>([]);

  // State for ForkModal target job and messages
  const [forkModalData, setForkModalData] = useState<{
    jobId: string;
    messages: any[];
    summaryData?: string;
    startInSummaryMode?: boolean;
  } | null>(null);

  const a = useRef([]);

  // Budget info is now managed by the useBudget hook
  const [agentState, setAgentState] = useState<AgentState | null>(null);
  const agentStateRef = useRef<AgentState | null>(null);
  const [isRollbackModalOpen, setIsRollbackModalOpen] = useState(false);
  const [rollbackRequestId, setRollbackRequestId] = useState<
    string | undefined
  >();
  const [rollbackStatus, setRollbackStatus] = useState<string | undefined>(
    "SUCCESS"
  );
  const [hideImportantActions, setHideImportantActions] = useState(true);
  const [pauseWasClicked, setPauseWasClicked] = useState(false);
  const [isPauseLoading, setIsPauseLoading] = useState(false);
  const [currentChunk, setCurrentChunk] = useState<Chunk | null>(null);
  const isEmergentUser = useIsEmergentUser();
  const isUploadAssetEnabled = useIsUploadAssetEnabled();
  const [createdBy, setCreatedBy] = useState(null);

  const [buildMode, setBuildMode] = useState<
    "brainstorming_requested" | "brainstorming_done" | "build" | null
  >(null);

  // Get state from tab context, location, and URL parameters
  // Fix: Improve containerId derivation logic to handle empty tab states
  const containerId =
    tabState?.containerId || location.state?.containerId;
  const task = tabState?.task || location.state?.task;
  const jobId = tabState?.jobId || location.state?.jobId || searchParams.get('id');
  const trajPath = tabState?.trajPath || location.state?.trajPath;
  const initialCommitId = tabState?.initial_commit_id;
  const isCloudFlow = tabState?.isCloudFlow;
  const clientRefId = tabState?.clientRefId;
  const [isQrCodeModalOpen, setIsQrCodeModalOpen] = useState(false);

  const { isMobile } = useScreenSize();

  const [showPreviewPrompt, setShowPreviewPrompt] = useState(false);


  const modelName = tabState?.modelName;

  const {
    vscodeUrl,
    vscodePassword,
    showVSCodeDialog,
    setVSCodeUrl,
    setVSCodePassword,
    setShowVSCodeDialog,
    handleVSCodeLink,
    handleOpenVSCodeUrl,
  } = useVSCode({
    isCloudFlow,
  });

  const {
    previewUrl,
    shareableUrl,
    refresh: refreshPreviewData,
    isLoading: isPreviewLoading,
    error: previewError
  } = usePreviewUrls({
    jobId: activeTab === tabId ? jobId : undefined,
    containerId: activeTab === tabId ? containerId : undefined,
    isCloudFlow,
    onVSCodeUrlChange: setVSCodeUrl,
    onVSCodePasswordChange: setVSCodePassword,
    autoFetch: true,
  });

  const {
    modifiedFiles,
    showDiffModal,
    setShowDiffModal,
    selectedFile,
    setSelectedFile,
    handleShowDiff,
  } = useDiff({
    containerId,
    jobId,
    isCloudFlow,
    initialCommitId,
  });

  // RTK Query hook for fetching job details
  const [getJobDetails] = useLazyGetJobQuery();

  // Get container-specific state from Redux with safe fallback
  const containerState = useAppSelector(
    (state) =>
      state.container?.containers?.[containerId] || {
        isInitialized: false,
        isJobPolling: false,
        jobDetails: undefined,
        trajectory: [],
        lastProcessedStep: -1,
        taskState: null,
      }
  );

  // Redux hooks for pending artifacts
  const pendingArtifacts = useAppSelector(state => selectPendingArtifacts(state, tabId)); // Use tabId prop to get artifacts for this specific tab

  const [jobDetails, setCurrentJobDetails] = useState<{
    job_id: string;
    traj_path: string;
    initial_commit_id?: string;
    createdBy: string;
    createdAt: string;
    forked:{
      parent_job_id: string | null;
      parent_job_title: string | null;
    },
    asset_upload_enabled: boolean;
    modelName: string;
    promptName: string;
    promptVersion: string;
    costLimit: number;
    agentName: string;
    dynamic_preview_subdomain: string;
    env_image: string;
    isPublished: boolean;
  }>({
    job_id: jobId || "",
    traj_path: trajPath || "",
    initial_commit_id: initialCommitId,
    createdBy: tabState?.created_by,
    createdAt: tabState?.createdAt,
    forked: {
      parent_job_id: tabState?.forked?.parent_job_id,
      parent_job_title: tabState?.forked?.parent_job_title,
    },
    asset_upload_enabled: tabState?.asset_upload_enabled || false,
    modelName: tabState?.modelName,
    promptName: tabState?.promptName,
    promptVersion: tabState?.promptVersion,
    costLimit: tabState?.costLimit,
    agentName: tabState?.agentName,
    dynamic_preview_subdomain: tabState?.dynamic_preview_subdomain,
    env_image: tabState?.env_image,
    isPublished: tabState?.isPublished || false,
  });

  // Use the useBudget hook to manage budget information
  const { budgetInfo, fetchBudgetInfo } = useBudget({
    jobId,
    initialFetch: true, // We'll fetch manually when needed
  });



  // Update tab state if we got state from location
  useEffect(() => {
    // Only update if we have location state and it's not already in tab state
    if (
      location.state?.containerId &&
      (!tabState?.containerId ||
        tabState.containerId !== location.state.containerId)
    ) {
      updateTabState(tabId, {
        ...location.state,
        isActive: true,
        podIsPaused: podIsPaused, // Ensure the tab is marked as active
      });
    }
  }, [location.state, tabState?.containerId, tabId, updateTabState, podIsPaused]); // Only depend on specific properties

  // Initialize container and set job details
  useEffect(() => {
    // Only require jobId initially - containerId will be set by fetchJobDetails
    if (!jobId) {
      console.error("Missing job ID");
      return;
    }

    // Initialize container if we have containerId and it's not already initialized
    if (containerId && !containerState.isInitialized) {
      dispatch(initializeContainer(containerId));
      dispatch(setInitialized({ containerId, isInitialized: true }));
      // Associate this container with the current tab
      dispatch(setTabContainer({ tabId, containerId }));
      // Set this container as active for the current tab
      dispatch(setActiveContainerForTab(tabId));
    }

    // Set job details if available
    if (containerId && jobId && trajPath) {
      dispatch(
        setJobDetails({
          containerId,
          details: {
            job_id: jobId,
            traj_path: trajPath,
            initial_commit_id: initialCommitId,
          },
        })
      );
    }
  }, [
    containerId,
    jobId,
    trajPath,
    initialCommitId,
    containerState.isInitialized,
    dispatch,
  ]);

  const fetchJobDetails = useCallback(async () => {
    if (jobId) {
      setIsJobDetailsLoading(true);
      try {
        const response = await getJobDetails(jobId);
        if ('error' in response) {
          throw new Error('Failed to fetch job details');
        }
        const jobDetails = response.data;
        if (!jobDetails) {
          throw new Error('No job data received');
        }

        // Get existing tab state to preserve properties like showCase
        const existingTabState = getTabState(tabId) || {};

        const tabState = {
          ...existingTabState, // Preserve existing properties
          containerId: jobDetails.payload.container_id || jobDetails.id,
          initial_commit_id: jobDetails.payload.initial_commit_id,
          task: jobDetails.payload?.original_task || jobDetails.payload.task,
          jobId: jobDetails.id,
          trajPath,
          tabId: tabId,
          fromJobList: true,
          isCloudFlow: jobDetails.payload?.is_cloud,
          clientRefId: jobDetails.client_ref_id,
          modelName: jobDetails.payload.model_name,
          promptName: jobDetails.payload.prompt_name,
          promptVersion: jobDetails.payload.prompt_version,
          costLimit: jobDetails.payload.per_instance_cost_limit,
          agentName: jobDetails.payload.agent_name,
          portMapping: jobDetails.payload.portMapping,
          created_by: jobDetails.created_by,
          podIsPaused: podIsPaused,
          base64_image_list: jobDetails.payload.base64_image_list,
          dynamic_preview_subdomain: jobDetails.payload.dynamic_preview_subdomain,
          env_image: jobDetails.payload.env_image,
          isPublished: jobDetails.payload.is_persisted
        };

        setCreatedBy(jobDetails.created_by as any);


        setCurrentJobDetails({
          job_id: jobId,
          traj_path: trajPath,
          initial_commit_id: initialCommitId,
          createdBy: jobDetails.created_by,
          forked: jobDetails.payload.forked,
          modelName: jobDetails.payload.model_name,
          promptName: jobDetails.payload.prompt_name,
          promptVersion: jobDetails.payload.prompt_version,
          costLimit: jobDetails.payload.per_instance_cost_limit,
          agentName: jobDetails.payload.agent_name,
          dynamic_preview_subdomain: jobDetails.payload.dynamic_preview_subdomain,
          createdAt: jobDetails.created_at,
          asset_upload_enabled: jobDetails.payload.asset_upload_enabled,
          env_image: jobDetails.payload.env_image,
          isPublished: jobDetails.payload.is_persisted || false
        });

        setGithubDetails(jobDetails.payload.source_repo);
        setTargetRepo(jobDetails.payload.target_repo);

        // Update tab state with fresh job details
        updateTabState(tabId, tabState);
        setTabs((prevTabs) =>
          prevTabs.map((tab) =>
            tab.id === tabId ? { ...tab, title: jobDetails.payload?.original_task || jobDetails.payload.task, state: tabState } : tab
          )
        );

        if(existingTabState.showCase){
          togglePanel({ panelName: "showUrlPreviewPanel", value: true });
        }

        setIsJobDetailsLoading(false);
      } catch (error) {
        // Capture error with PostHog
        captureError(error instanceof Error ? error : String(error), {
          jobId,
          containerId,
          context: "fetch_job_details",
        });

        toast({
          title: "Error fetching task details",
          description: "Please try again later",
          variant: "destructive",
        });
        removeTab(tabId);
        setIsJobDetailsLoading(false);
      }
    }
  }, [
    jobId,
    trajPath,
    tabId,
    podIsPaused,
    initialCommitId,
    containerId,
    updateTabState,
    setTabs,
    toast,
    removeTab,
    getJobDetails,
  ]);

  // Use the usePublish hook to manage app publishing
  const { isPublishing, publishApp } = usePublish({
    jobId,
    onPublishSuccess: fetchJobDetails,
  });

  useEffect(() => {
    // Only fetch job details if this tab is active
    if (activeTab === tabId) {
      fetchJobDetails();
    }
  }, [jobId, activeTab, tabId]);

  const shouldPoll = useMemo(() => {
    // Check if this tab is currently the active tab
    return tabId === activeTab && tabState?.isActive !== false;
  }, [tabId, activeTab, tabState?.isActive]);

  const { resetPolling, connectionStatus, autoReconnect, setAutoReconnect } =
    usePollingWorker({
      isCloudFlow,
      containerId: tabState?.containerId,
      jobId: tabState?.jobId,
      trajPath: tabState?.trajPath,
      isActive: shouldPoll,
      onCurrentChunkResult: (data) => {
        if (data === "") {
          setCurrentChunk(null);
          return;
        }

        // // Check if this chunk is already in the trajectory
        if (data && data.chunk && data.chunk.request_id) {
          // Check both id and request_id fields to ensure we catch all matches
          const isInTrajectory = aiMessages?.some((msg) => {
            // Check if the message ID matches the chunk request_id
            if (msg.id === data.chunk.request_id) {
              return true;
            }

            // Check if the message has a request_id field that matches
            if (msg.request_id === data.chunk.request_id) {
              return true;
            }

            // Also check if the content and action match (as a fallback)
            if (
              msg.content === data.chunk.data?.thought &&
              msg.action === data.chunk.data?.tool_name
            ) {
              return true;
            }

            return false;
          });

          // If it's already in the trajectory, don't show the typing message
          if (isInTrajectory) {
            setCurrentChunk(null);
            return;
          }
        }

        setCurrentChunk(data);
      },
      onJobDetailsResult: (data) => {
        if (!containerId || !data) return;
        dispatch(setJobDetails({ containerId, details: data }));
      },
      onTrajectoryResult: async (data) => {
        if (a.current.length === 0) {
          a.current = data.length;
        }

        if (a.current.length !== data.length) {
          if (currentChunk?.chunk?.agent_name !== "SkilledAssistant") {
          }
          a.current = data.length;
        }
        dispatch(setTrajectory({ containerId, trajectory: data }));
      },
      onAgentStateResult: (agent) => {
        agentStateRef.current = agent;
        setAgentState(agent);

        // Reset pause click state when agent state changes
        if (
          agent.agent_running !== agentState?.agent_running ||
          agent.job_running !== agentState?.job_running
        ) {
          setPauseWasClicked(false);
        }

        // Update pod state based on agent status
        const isArchived = agent.agent_status_new === "archived";
        const isPaused = agent.agent_status_new === "paused" || isArchived;

        setArchivedPod(isArchived);
        setPodIsPaused(isPaused);
      },
      onBrainStormingResult: (data) => {
        setBuildMode(data.chat_mode);
      },
      onError: (error) => {
        // Error handled by polling worker
      },
    });

  const handleInfoPanelVisibility = (visible: boolean) => {
    togglePanel({ panelName: "showInfoPanel", value: visible });
  };

  const handleDeployClick = useCallback(() => {
    togglePanel({ panelName: "showDeployPanel", value: true });

    // Track deploy panel toggle
    trackFeatureUsage("deploy_panel_toggle", {
      jobId,
      containerId,
      isVisible: !showDeployPanel, // Toggle will make it the opposite of current state
      tabId,
    });
  }, [togglePanel, showDeployPanel, jobId, containerId, tabId]);

  const handlePublishClick = useCallback(() => {
    publishApp();
  }, [publishApp]);



  // Function to handle opening a task from deployed apps (similar to Home.tsx)
  const handleJobClick = useCallback(
    (job: any) => {
      if (!job.payload.task) {
        console.error("No task data available");
        return;
      }

      const containerId = job.payload.container_id || job.id;

      if (!containerId) {
        console.error("No container ID available");
        return;
      }

      const existingTab = getTabByJobId(job.id);
      if (existingTab) {
        setActiveTab(existingTab.id);
        return;
      }

      // Use job.id as tab ID to ensure consistency
      const newTabId = job.id;

      const tabState = {
        jobId: job.id,
        containerId: containerId,
        task: job.payload?.original_task || job.payload.task,
        trajPath: job.traj_path,
        initial_commit_id: job.initial_commit_id,
        isCloudFlow: job.payload.is_cloud_flow || false,
        clientRefId: job.payload.client_ref_id,
        modelName: job.payload.model_name,
        promptName: job.payload.prompt_name,
        promptVersion: job.payload.prompt_version,
        costLimit: job.payload.per_instance_cost_limit,
        agentName: job.payload.agent_name,
        portMapping: job.payload.portMapping,
        created_by: job.created_by,
        createdAt: job.created_at,
        isActive: true,
      };

      // Create new tab
      setTabs((prevTabs) => [
        ...prevTabs,
        {
          id: newTabId,
          title: `${job.payload.task}`,
          path: "/chat",
          state: tabState,
        },
      ]);

      // Update tab state
      updateTabState(newTabId, tabState);

      // Set active tab and navigate
      setActiveTab(newTabId);
      navigate(`/chat?id=${job.id}`);
    },
    [getTabByJobId, setActiveTab, setTabs, updateTabState, navigate]
  );

  const initialMessages: Message[] = useMemo(()=>{
  return  task
    ? [
        {
          id: `user-task`,
          role:  "user",
          content: task,
          container_id: containerId,
          timestamp: jobDetails.createdAt || new Date().toISOString(),
          base64_image_list:
            containerState.trajectory?.[0]?.base64_image_list || [],
        },
      ]
    : [];
  },[task, containerId])
  // Cast the messages to our extended Message type
  const { messages: rawMessages, setMessages: setAiMessages } = useChat({
    initialMessages: initialMessages,
  });

  const rawAiMessages = rawMessages as Message[];



  const aiMessages = useMemo(() => {
    return rawAiMessages;
  }, [
    JSON.stringify(
      rawAiMessages.map((msg) => ({
        id: msg.id,
        content: msg.content,
        role: msg.role,
        timestamp: msg.timestamp,
        base64_image_list: msg.base64_image_list,
        subagent_trajectory: msg.subagent_trajectory,
        forked_job_id: msg.forked_job_id,
        function_name: (msg as any).function_name,
        artifacts: (msg as any).artifacts,
      }))
    ),
  ]);

  const { processTrajectory } = useTrajectoryProcessor({
    task,
    containerId,
    jobId,
    showSubagentPanel,
    selectedMessageId,
    selectedSubagent,
    setSelectedSubagentMessages,
    setIsSubagentActive,
    setAiMessages,
    getCurrentMessages: () => aiMessages, // Pass function to get current messages
  });

  



  const processTrajectoryData = useCallback((pendingMessage?: any) => {
    if (!containerState.trajectory || containerState.trajectory.length === 0) {
      return;
    }

    const data = [...containerState.trajectory];

    let lastPendingMessage = null;

    if (
      data.length > 0 &&
      data[data.length - 1] &&
      data[data.length - 1].agent_name === "EmergentAssistant"
   ) {
      surveyService.triggerEvent("agent-finished", {
        job_id: containerId,
      });
    }

    if (pendingMessage) {
      const foundMatch = data.some(
        (item) =>
          item.human_message === pendingMessage.content &&
          item.timestamp > pendingMessage.timestamp
      );

      const olderThan5Minutes =
        new Date().getTime() - new Date(pendingMessage.timestamp).getTime() >
        5 * 60 * 1000;

      if (foundMatch || olderThan5Minutes) {
        clearPendingMessage(tabId, pendingMessage.content);
      } else {

        lastPendingMessage = {
          id: `user-${Date.now()}`,
          role: "user",
          content: pendingMessage.content,
          containerId: containerId,
          timestamp: new Date().toISOString(),
          status: "pending",
          base64_image_list: pendingMessage.base64_image_list,
          artifacts: pendingMessage.artifacts, // Include artifacts for high-quality display
        };
      }
    }

    processTrajectory(data, lastPendingMessage);
  }, [
    containerState.trajectory,
    processTrajectory,
    tabId,
    containerId,
    clearPendingMessage,
  ]);

  const [bulkWriteExists, setBulkWriteExists] = useState(false);

  // Separate useEffect for bulk write detection and error handling
  useEffect(() => {
    const assistantMessages = aiMessages.filter(
      (msg) => msg.role === "assistant"
    );
    const bulkWriteExistsMessage = assistantMessages.find(
      (msg) =>
        msg.function_name?.includes("bulk_file")||
        msg.function_name?.includes("replace") ||
        msg.action === "finish"
    );
    const lastAssistantMessage =
      assistantMessages[assistantMessages.length - 1];

    const forkedJob = jobDetails?.forked?.parent_job_id;

    if (bulkWriteExistsMessage || forkedJob) {
      setBulkWriteExists(true);

      // Check if we should auto-open the preview panel for bulk write
      // Only on desktop (screen width >= 768px) and only once per tab
      const isDesktop = window.innerWidth >= 768;
      const isLastMessage = lastAssistantMessage && (
        lastAssistantMessage.function_name?.includes("bulk_file") ||
        lastAssistantMessage.action === "finish"
      );

      setShowPreviewPrompt(true);

      if (isDesktop && isLastMessage && !bulkWritePreviewOpenedRef.current) {
         refreshPreviewData().then(() => {
          togglePanel({ panelName: "showUrlPreviewPanel", value: true });
          bulkWritePreviewOpenedRef.current = true;
        });
      }
    } else {
      setBulkWriteExists(false);
    }

  }, [aiMessages, jobDetails?.forked?.parent_job_id, tabId]);

  // Separate useEffect for pending message error handling
  useEffect(() => {
    const assistantMessages = aiMessages.filter((msg) => msg.role === "assistant");
    const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];

    const pendingMessage = tabState?.pendingMessage;
    if (pendingMessage && lastAssistantMessage?.error_ts) {
      // Convert both timestamps to epoch time (milliseconds)
      const pendingTimestamp = new Date(pendingMessage.timestamp).getTime();
      const errorTimestamp = new Date(lastAssistantMessage.error_ts).getTime();
      // Clear pending message if it's older than the last error timestamp
      if (pendingTimestamp < errorTimestamp) {
        setFailedPendingValue(pendingMessage.content);
        // Use the function directly without including it in dependencies
        clearPendingMessage(tabId, pendingMessage.content);
      }
    }
  }, [aiMessages, tabState?.pendingMessage, tabId]);

  // Separate useEffect for trajectory processing
  useEffect(() => {
    const pendingMessage = tabState?.pendingMessage;
    processTrajectoryData(pendingMessage);
  }, [containerState.trajectory, tabId, tabState?.pendingMessage]);

  useEffect(() => {
    let checkJobDetailsInterval: NodeJS.Timeout | undefined;
    let timeoutId: NodeJS.Timeout | undefined;

    return () => {
      if (checkJobDetailsInterval) clearInterval(checkJobDetailsInterval);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [containerId, dispatch]);

  useEffect(() => {
    if (showSubagentPanel && aiMessages.length > 0) {
      const lastMessage = aiMessages[aiMessages.length - 1];
      if (
        lastMessage &&
        lastMessage.subagent_trajectory &&
        lastMessage.subagent_trajectory.length > 0
      ) {
        // Only update if we're viewing the same agent and there are new messages
        if (
          lastMessage.agent_name === selectedSubagentMessages[0]?.agent_name
        ) {
          setSelectedSubagentMessages(lastMessage.subagent_trajectory);
        }
      }
    }
  }, [showSubagentPanel, aiMessages]);

  useEffect(() => {
    if (showSubagentPanel && selectedMessageId && aiMessages.length > 0) {
      // Find the message we're currently viewing
      const selectedMessage = aiMessages.find(
        (msg: Message) => msg.id === selectedMessageId
      );
      if (
        selectedMessage &&
        selectedMessage.subagent_trajectory &&
        selectedMessage.subagent_trajectory.length > 0
      ) {
        // Only update if we're viewing the same agent and there are new messages
        if (selectedMessage.agent_name === selectedSubagent) {
          setSelectedSubagentMessages(selectedMessage.subagent_trajectory);
        }
      }
    }
  }, [showSubagentPanel, selectedMessageId, aiMessages, selectedSubagent]);

  // Clear selectedMessageId when subagent panel is closed
  useEffect(() => {
    if (!showSubagentPanel && selectedMessageId) {
      setSelectedMessageId(undefined);
    }
  }, [showSubagentPanel, selectedMessageId]);

  useEffect(() => {
    if (
      showSubagentPanel &&
      selectedStepNum !== undefined &&
      containerState.trajectory
    ) {
      const step = containerState.trajectory.find(
        (t) => t.step_num === selectedStepNum
      );
      if (step?.subagent_trajectory) {
        setSelectedSubagentMessages(step.subagent_trajectory);
      }
    }
  }, [showSubagentPanel, selectedStepNum, containerState.trajectory]);

  useEffect(() => {
    // Update subagent panel if we're viewing a message and there are new messages
    if (showSubagentPanel && selectedMessageId && aiMessages.length > 0) {
      const selectedMessage = aiMessages.find(
        (msg: Message) => msg.id === selectedMessageId
      );
      if (
        selectedMessage &&
        selectedMessage.subagent_trajectory &&
        selectedMessage.subagent_trajectory.length > 0
      ) {
        setSelectedSubagentMessages(selectedMessage.subagent_trajectory);
      }
    }
  }, [showSubagentPanel, selectedMessageId, aiMessages]);

  const handleSendMessage = useCallback(
    (content: string, images: ResponseImageData[], artifacts?: PendingArtifact[]) => {
      const messageId = `user-${Date.now()}`;

      // Track message sent event
      trackChatInteraction("message_sent", {
        jobId,
        containerId,
        messageId,
        messageLength: content.length,
      hasImages: images.length > 0,
        imageCount: images.length,
        tabId,
      });

      const newMessage: Message = {
        id: messageId,
        role: "user",
        content,
        containerId: containerId,
        timestamp: new Date().toISOString(),
        status: "pending", // Mark as pending when first created
        base64_image_list: images,
      };

      // Add to aiMessages with pending status
      setAiMessages((prev: any) => [...prev, newMessage]);

      if (content.trim()) {
        setPendingMessage(tabId, messageId, content, images, artifacts);
      }
    },
    [containerId, jobId, tabId]
  );

  const handleSubmit = handleSendMessage;

  // Removed unused function

  const handleSubagentClick = useCallback(
    (message: any) => {
      if (message.subagent_trajectory?.length) {
        const expertise = message.expertise_type || message.function_name;
        setSelectedSubagentMessages(message.subagent_trajectory);
        setSelectedSubagent(expertise);
        setSelectedMessageId(message.id);
        togglePanel({ panelName: "showSubagentPanel", value: true });

        // Track subagent selection
        trackFeatureUsage("subagent_selected", {
          jobId,
          containerId,
          expertise,
          messageId: message.id,
          subagentTrajectoryLength: message.subagent_trajectory.length,
          tabId,
        });
      }
    },
    [jobId, containerId, tabId]
  );

  const handlePreviewClick = useCallback(async () => {
    // Only refresh if panel is not already open
    if (!showUrlPreviewPanel && refreshPreviewData) {
      try {
        await refreshPreviewData();
      } catch (error) {
        // Error handled silently
      }
    }

    togglePanel({ panelName: "showUrlPreviewPanel", value: true });

    // Track preview panel toggle
    trackFeatureUsage("preview_panel_toggle", {
      jobId,
      containerId,
      isVisible: !showUrlPreviewPanel, // Toggle will make it the opposite of current state
      tabId,
      previewUrl,
    });
  }, [togglePanel, showUrlPreviewPanel, jobId, containerId, tabId, previewUrl, refreshPreviewData]);

  const handleOpenForkModal = useCallback((targetJobId: string, targetMessages: any[]) => {
    // Store the target job ID and messages for the ForkModal
    setForkModalData({
      jobId: targetJobId,
      messages: targetMessages,
    });

    // Set the modal state to open the ForkModal
    setModalOpen((prev) => ({
      ...prev,
      fork: true,
    }));

  }, []);

  const handleOpenForkModalWithSummary = useCallback((targetJobId: string, summaryData: string) => {

    // Store the target job ID and summary data for the ForkModal
    setForkModalData({
      jobId: targetJobId,
      messages: [], // No messages needed when we have summary data
      summaryData: summaryData,
      startInSummaryMode: true,
    });

    // Set the modal state to open the ForkModal
    setModalOpen((prev) => ({
      ...prev,
      fork: true,
    }));

  }, []);

  const handleOpenUploadAssetsModal = useCallback((files?: File[]) => {
    setUploadAssetsInitialFiles(files || []);
    setShowUploadAssetsModal(true);
  }, []);


  const handleSubagentSubmit = useCallback(
    async (message: string, images?: ResponseImageData[]) => {
      if (!selectedSubagent || !jobId) return;

      // Track subagent message sent
      trackChatInteraction("message_sent", {
        jobId,
        containerId,
        messageLength: message.length,
        hasImages: images ? images.length > 0 : false,
        imageCount: images ? images.length : 0,
        tabId,
        isSubagent: true,
        subagentType: selectedSubagent,
      });

      // Create a new message for the subagent
      const newMessage = {
        id: `subagent-${Date.now()}`,
        role: "user" as const,
        content: message,
        timestamp: new Date().toISOString(),
        agent_name: selectedSubagent,
        base64_image_list: images,
      };

      // Add message to the selected subagent's messages
      setSelectedSubagentMessages((prev: ImportedAgentMessage[] = []) => [
        ...prev,
        newMessage,
      ]);
    },
    [selectedSubagent, jobId, containerId, tabId]
  );

  // Track if we've already initialized the panel state for this tab to avoid repeated toggles
  const panelInitializedRef = useRef(false);

  // Track if we've already opened the preview panel for bulk write to avoid repeated toggles
  const bulkWritePreviewOpenedRef = useRef(false);

  // Reset bulk write preview opened flag when tab changes or job changes
  useEffect(() => {
    bulkWritePreviewOpenedRef.current = false;
  }, [tabId, jobId]);

  // Only run this effect when specific tab properties change, not on every polling update
  useEffect(() => {
    const tabData = getTabState(tabId);
    if (tabData && user) {
      if (tabData.showCase) {
        setIsInputActive(false);
        if (!panelInitializedRef.current) {
          togglePanel({ panelName: "showUrlPreviewPanel", value: true });
          panelInitializedRef.current = true;
        }
      }
      if (tabData.created_by == user?.id) {
        setHideImportantActions(false);
        // For owners, we don't automatically open the URL preview panel
        // but we also don't force close it if they opened it manually
      } else {
        setHideImportantActions(true);
        if (!panelInitializedRef.current) {
          togglePanel({ panelName: "showUrlPreviewPanel", value: false });
          panelInitializedRef.current = true;
        }
      }
    } else {
      setHideImportantActions(true);
    }
  }, [tabState?.showCase, tabState?.created_by, user?.id, jobId]); // Only depend on specific properties


  // Fetch budget info when the tab becomes active or when messages change
  useEffect(() => {
    if (user && session && activeTab === tabId && jobId) {
      fetchBudgetInfo();
    }
  }, [
    user,
    session,
    activeTab,
    tabId,
    jobId,
    aiMessages.length,
    fetchBudgetInfo,
  ]);

  const footerComponent = useMemo(() => {
    if( buildMode == "brainstorming_requested" || buildMode == "brainstorming_done"){
      const buildModeChunk = agentState?.agent_running ? {
        chunk: {
          data: {
            thought: "Analyzing your request and preparing the build environment...",
            tool_name: buildMode === "brainstorming_done" ? "build_mode" : null,
          }
        }
      } : null;

      return () => (
        <div className="mt-4">
          {agentState?.agent_running  && (
            <>
              <StableTypingAnimation
                initialText={"Analyzing your request"}
                currentChunk={buildModeChunk}
              />
              <div className="h-32" />
            </>
          )}
        </div>
      );
    }

    if(isSubagentActive){
      return ()=> <></>;
    }

    return () => (
      <AgentFooter
        text={text}
        rollbackStatus={rollbackStatus}
        agentStatus={agentState?.agent_running}
        isSubagentChunk={
          ((currentChunk &&
            currentChunk.chunk &&
            currentChunk?.chunk?.data &&
            currentChunk?.chunk?.agent_name === "SkilledAssistant") ||
          (aiMessages.length > 0 &&
            aiMessages[aiMessages.length - 1].agent_name === "SkilledAssistant"))  
        }
        currentChunk={currentChunk}
      />
    );
  }, [
    text,
    rollbackStatus,
    agentState?.agent_running,
    currentChunk?.chunk?.agent_name,
    currentChunk?.chunk?.request_id,
    currentChunk?.chunk?.data,
    aiMessages,
    buildMode,
    isSubagentActive,
  ]);

  const userInitials = useMemo(() => {
    return session?.user?.email
      ? session.user.email
          .split("@")[0]
          .split(".")
          .map((n) => n[0])
          .join("")
          .toUpperCase()
      : "U";
  }, [session?.user?.email]);

  const virtuosoData = useMemo(() => {
    // While job details are loading, don't show any messages to prevent flash
    if (isJobDetailsLoading) {
      return [];
    }

    // For forked jobs, don't show any messages until we have more than 1 message to prevent brief flash
    if (jobDetails?.forked?.parent_job_id && aiMessages.length <= 1) {
      return [];
    }

    return aiMessages;
  }, [aiMessages, jobDetails?.forked?.parent_job_id, isJobDetailsLoading]);

  const [isAtBottom, setIsAtBottom] = useState(true);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const userScrolledRef = useRef(false);
  const atBottomTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastBottomStateRef = useRef(true);
  const scrollEndedTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function for scroll to bottom
  const scrollToBottom = () => {
    if (virtuosoRef.current) {
      // Clear any pending timeouts
      if (atBottomTimeoutRef.current) {
        clearTimeout(atBottomTimeoutRef.current);
        atBottomTimeoutRef.current = null;
      }

      virtuosoRef.current.scrollToIndex({
        index: aiMessages.length - 1,
        behavior: "smooth",
      });

      // Set states with a slight delay to avoid race conditions
      setTimeout(() => {
        setShowScrollButton(false);
        setIsAtBottom(true);
        userScrolledRef.current = false;
        lastBottomStateRef.current = true;
      }, 100);
    }
  };

  // Function to check if we're really at the bottom and update state accordingly
  const checkIfReallyAtBottom = (atBottom: boolean) => {
    // If the state changed from not-at-bottom to at-bottom
    if (!lastBottomStateRef.current && atBottom) {
      // Set a timeout to confirm this is a real at-bottom state, not just a momentary false positive
      atBottomTimeoutRef.current = setTimeout(() => {
        if (virtuosoRef.current && atBottom) {
          setIsAtBottom(true);
          setShowScrollButton(false);
          userScrolledRef.current = false;
        }
        atBottomTimeoutRef.current = null;
      }, 150); // Small delay to ensure stability
    }
    // If the state changed from at-bottom to not-at-bottom
    else if (lastBottomStateRef.current && !atBottom) {
      setIsAtBottom(false);
      setShowScrollButton(true);
      userScrolledRef.current = true;
    }

    // Update the last known state
    lastBottomStateRef.current = atBottom;
  };

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Clean up timeouts on unmount
      if (atBottomTimeoutRef.current) {
        clearTimeout(atBottomTimeoutRef.current);
      }
      if (scrollEndedTimeoutRef.current) {
        clearTimeout(scrollEndedTimeoutRef.current);
      }
    };
  }, []);

  const [isWakingUp, setIsWakingUp] = useState(false);
  const [apiCallComplete, setApiCallComplete] = useState(false);
  const [shouldReloadIframe, setShouldReloadIframe] = useState(false);
  const [setupLoaderComplete, setSetupLoaderComplete] = useState(false);
  const [showSetupLoader, setShowSetupLoader] = useState(false);
  const { showErrorToast } = useErrorToast();

  useEffect(() => {
    // This empty effect will cause a re-render when isWakingUp changes
  }, [isWakingUp]);

  // Handle setup loader state transitions
  useEffect(() => {
    // Show setup loader when API call is in progress or needed
    if (tabState.needsApiCall || tabState.apiCallInProgress) {
      setShowSetupLoader(true);
      setSetupLoaderComplete(false);
    }
    // When API call completes, trigger completion animation
    else if (showSetupLoader && !setupLoaderComplete) {
      setSetupLoaderComplete(true);
    }
  }, [tabState.needsApiCall, tabState.apiCallInProgress, showSetupLoader, setupLoaderComplete]);

  // Handle setup loader completion
  const handleSetupLoaderComplete = useCallback(() => {
    setShowSetupLoader(false);
    setSetupLoaderComplete(false);
  }, []);

  // Handle iframe reload after successful wakeup
  const handleIframeReloaded = useCallback(() => {
    // Reset the flag after the iframe has been reloaded
    setShouldReloadIframe(false);
  }, []);

  const resumePod = useCallback(() => {
    if (!jobId || !containerId) return;

    trackFeatureUsage("wake_up_clicked", {
      jobId,
    });

    setIsWakingUp(true);
    togglePanel({ panelName: "showUrlPreviewPanel", value: false });
    // Reset the iframe reload flag
    setShouldReloadIframe(false);

    const startWakeupProcess = () => {
      setTimeout(() => {
        try {
          setShouldReloadIframe(false);
          agentApi
            .restartEnvironment(jobId)
            .then((response) => {
              if (response) {

                setTimeout(() => {
                  setApiCallComplete(true);
                  setPauseWasClicked(true);
                  setPodIsPaused(false);
                  setIsWakingUp(false);
                  setApiCallComplete(false);
                  // Set flag to reload iframe after wakeup is complete
                  setShouldReloadIframe(true);

                  trackFeatureUsage("wake_up_successful", {
                    jobId,
                  });
                }, 5500);
              }
            })
            .catch((error) => {
              // Extract error message
              let errorMessage =
                "Failed to restart the environment. Please try again.";

              // Try to get a more specific error message
              if (error.response && error.response.data) {
                errorMessage =
                  error.response.data.message ||
                  error.response.data.error ||
                  errorMessage;
              } else if (error.message) {
                errorMessage = error.message;
              }

              showErrorToast("Error Waking Up Agent", errorMessage);

              trackFeatureUsage("wake_up_failed", {
                jobId,
                errorMessage
              });

              setIsWakingUp(false);
              setApiCallComplete(false);
            });
        } catch (error) {
          showErrorToast(
            "Error Waking Up Agent",
            "Failed to initiate the wakeup process. Please try again."
          );
          trackFeatureUsage("wake_up_failed", {
            jobId,
            error: JSON.stringify(error)
          });
          setIsWakingUp(false);
        }
      }, 100);
    };

    startWakeupProcess();
  }, [jobId, containerId]);

  const completeWakeup = useCallback(() => {
    // No-op - API call is now made directly in resumePod
  }, []);

  const handlePausePod = async () => {
    if (!jobId || !containerId) return;

    try {
      const response = await agentApi.pauseEnvironment(jobId);
      if (response.status === 200 || response.status === 201) {
        setPauseWasClicked(true);
        setPodIsPaused(true);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to pause agent",
        variant: "destructive",
      });
    }
  };

  // Centralized pause handler for both ChatInput and SubagentButton
  const handlePauseAgent = useCallback(
    async ({
      origin = "MainInput",
    }: {
      origin?: "MainInput" | "SubagentButton";
    }) => {
      if (!jobDetails?.job_id || !agentState?.agent_running) {
        return;
      }

      setIsPauseLoading(true);
      setPauseWasClicked(true);

      try {
        const response = await agentApi.pauseJob({
          job_id: jobDetails.job_id,
          agent_name:
            origin === "SubagentButton"
              ? "SkilledAssistant"
              : tabState?.agentName,
        });
        if (response.status === 200 || response.status === 201) {
          // Success - the polling will update agentState and reset loading state
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to pause agent",
          variant: "destructive",
        });
        // Reset loading state on error
        setIsPauseLoading(false);
        setPauseWasClicked(false);
      }
    },
    [jobDetails?.job_id, agentState?.agent_running, tabState?.agentName, toast]
  );

  // Reset pause loading state when agent stops running
  useEffect(() => {
    if (!agentState?.agent_running && isPauseLoading) {
      setIsPauseLoading(false);
    }
  }, [agentState?.agent_running, isPauseLoading]);

  const sendMessageToAgent = async ({
    content,
  }: {
    content: string;
    images: ResponseImageData[];
  }) => {
    try {
      // For cloud flow, use submitCloudJob with resume true
      const payload = {
        processor_type: "env_only",
        is_cloud: true,
        task: content,
        prompt_name: "",
        prompt_version: "latest",
        work_space_dir: "",
        model_name: modelName || "claude-3-5-sonnet-20241022",
        env_image: "",
        base64_image_list: [],
        human_timestamp: new Date().toISOString(),
      };

      await agentApi.submitCloudJob(payload, jobDetails?.job_id, true);
    } catch (error) {
      removePendingMessage({ tabId, content });
      toast({
        title: "Error",
        description: "Failed to resume agent",
        variant: "destructive",
      });
    }
  };

  const [isInputActive, setIsInputActive] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: any) => {
      if ((event.metaKey || event.ctrlKey) && event.key === "/") {
        event.preventDefault();

        // Toggle the state
        setIsInputActive((prevState) => !prevState);
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  const isExpoImage = jobDetails?.env_image?.includes("expo")
  
  // Panel size management with different default sizes for each panel
  const [panelSizes, setPanelSizes] = useState({
    subagent: 45,
    info: 30,
    urlPreview: isExpoImage ? 35 : 50,
    deploy: 45,
    logs: 30, // Add logs panel size
  });


  // Track if user is currently resizing
  const [isResizing, setIsResizing] = useState(false);

  useEffect(() => {
    if (aiMessages.length > 0) {
      setCreatedBefore25April((prev) => {
        // @ts-ignore
        return isBeforeApril252024(aiMessages[0]?.timestamp) || prev;
      });
    }
  }, [aiMessages]);

  function isBeforeApril252024(timestamp?: string) {
    if (!timestamp) return false;
    const inputDate = new Date(timestamp);
    const targetDate = new Date("2025-04-25T00:00:00Z");
    return inputDate < targetDate;
  }

  const onShareLogs = (logs: string[]) => {

    const content = CUSTOM_TEXT.logsHeading + CUSTOM_TEXT.logsIdentifier + CUSTOM_TEXT.logsPrompt + logs.join("\n") +
      CUSTOM_TEXT.logsIdentifier;

    if (!agentState?.agent_running) {
      handleSendMessage(
        content,
        []
      );
      sendMessageToAgent({
        content: content,
        images: [],
      });
    } else {
      toast({
        title: "Agent is Running",
        description:
          "Please wait for agent to finish or stop it manually before sharing logs",
        variant: "destructive",
        duration: 2000,
      });
    }
  };


   // Calculate panel width based on which panel is open
  const getPanelWidth = () => {
    if (showUrlPreviewPanel) return panelSizes.urlPreview;
    if (showDeployPanel) return panelSizes.deploy;
    if (showSubagentPanel) return panelSizes.subagent;
    if (showInfoPanel) return panelSizes.info;
    if (showLogsPanel) return panelSizes.logs;
    return 0;
  };

  // Get the appropriate default size for the currently active panel (as percentage)
  const getPanelDefaultSize = () => {
    if (showUrlPreviewPanel) return isExpoImage ? 35 : 50; // Larger default for preview panel
    if (showDeployPanel) return 45; // Medium default for deploy panel
    if (showSubagentPanel) return 40; // Standard default for subagent panel
    if (showInfoPanel) return 30; // Smaller default for info panel
    if (showLogsPanel) return 30; // Medium-small default for logs panel
    return 30; // Fallback default
  };

  const panelWidth = getPanelWidth();
  const isAnyPanelOpen = panelWidth > 0;

    const { data: configData, refetch } = useGetConfigQuery();

  const submissionInProgressRef = useRef(false);

  // Upload selected files and create pending artifacts
  const uploadSelectedFiles = async (files: File[], clientRefId: string): Promise<PendingArtifact[]> => {
    const pendingArtifacts: PendingArtifact[] = [];

    for (const file of files) {
      try {
        // Create object URL for local preview
        const localPreviewUrl = URL.createObjectURL(file);

        // Upload all files as public by default
        const isImage = file.type.startsWith('image/');

        // Use 3-step flow for all files (public upload)
        const uploadRequest = {
          file_name: file.name,
          mime_type: file.type,
        };

        const uploadResponse = await agentApi.uploadArtifact(clientRefId, uploadRequest);
        await agentApi.uploadFileToUrl(uploadResponse.upload_url, file);

        pendingArtifacts.push({
          artifact_id: uploadResponse.artifact_id,
          entity_id: clientRefId,
          entity_type: "job",
          visibility: "public",
          file_name: file.name,
          description: "",
          file_path: uploadResponse.file_path,
          readByAgent: isImage, // Only images can be sent to agent
          mime_type: file.type,
          file_size: file.size,
          local_preview_url: localPreviewUrl,
          local_preview_base64: undefined,
        });
      } catch (error) {
        // Continue with other files even if one fails
      }
    }

    return pendingArtifacts;
  };

  const submitJOB = useCallback(async () => {
    if (submissionInProgressRef.current || tabState.apiCallInProgress) {
      return;
    }

    if (tabState.client_ref_id) {
      if (tabState.needsApiCall) {
        updateTabState(tabId, {
          ...tabState,
          needsApiCall: false,
        });
      }
      return;
    }

    submissionInProgressRef.current = true;

    try {
      const pendingClientRefId = tabState.pending_client_ref_id || crypto.randomUUID();

      updateTabState(tabId, {
        ...tabState,
        apiCallInProgress: true,
        pending_client_ref_id: pendingClientRefId,
        needsApiCall: false, // Clear this flag immediately
        fromShowCase: false,
        showCase: false,
        created_by: user?.id,
      });

      // configData is now available from RTK Query hook above
      if (tabState.experimental && !configData) {
        try {
          await refetch();
        } catch (configError) {
          // Error handled silently
        }
      }

      const {
        selectedAgent,
        task,
        model_name,
        per_instance_cost_limit,
        agentic_skills,
        portMappings,
        image,
        selectedImage,
        base64_image_list,
        repository,
        branch,
        experimental
      } = tabState;

      // EmbeddedTask already processed files and separated base64 images from artifact files
      // base64_image_list contains compressed images under 5MB
      // selectedFiles contains files that need to be uploaded as artifacts

      // Use the base64_image_list directly from EmbeddedTask processing
      const combinedBase64Images = base64_image_list || [];




      // Use selectedImage if image is not available (for backward compatibility)
      const dockerImage = image || selectedImage;

      // Ensure we have the required agent field with fallback
      const agentName = selectedAgent || tabState.agentName || tabState.agent_name;

      // Ensure we have the required model field with fallback
      const modelName = model_name || tabState.modelName || tabState.model;

      // Check if user has explicitly set values in advanced controls
      const hasUserSelectedAgent = tabState.selectedAgent && tabState.selectedAgent !== '';
      const hasUserSelectedModel = tabState.model_name && tabState.model_name !== '';
      const hasUserSelectedImage = (tabState.selectedImage || tabState.image) && (tabState.selectedImage !== '' || tabState.image !== '');

      // Prepare API payload with robust field handling
      // Priority: User selections > Experimental defaults > Regular defaults
      const payload = {
        // Handle custom agent selection - use user_prompt_id if available, otherwise use prompt_name
        ...(tabState.user_prompt_id ? {
          user_prompt_id: tabState.user_prompt_id,
          // Don't include prompt_name when using custom agent
        } : {
          prompt_name: hasUserSelectedAgent ? agentName :
                      (!experimental ? agentName : configData?.e1_experimental?.agent || agentName),
        }),
        prompt_version: "latest",
        work_space_dir: "",
        task: task,
        model_name: hasUserSelectedModel ? modelName :
                   (!experimental ? modelName : configData?.e1_experimental?.baseModel || modelName),
        per_instance_cost_limit: parseInt(per_instance_cost_limit?.toString() || "5", 10),
        agentic_skills: agentic_skills,
        portMapping: portMappings,
        env_image: hasUserSelectedImage ? dockerImage :
                  (!experimental ? dockerImage : configData?.e1_experimental?.dockerImage || dockerImage),
        plugin_version: configData?.plugin_library_version,
        base64_image_list: combinedBase64Images,
        repository: repository,
        branch: branch,
        human_timestamp: Date.now(),
        asset_upload_enabled: isUploadAssetEnabled,
      };

      trackFeatureUsage("new_job_submitted", {tabState, payload});

      if(task && !dockerImage){
        trackFeatureUsage("new_job_submitted_without_data", {tabState, payload});
        throw new Error("Task creation failed, please try again after a refresh.");
      }

      // Base64 conversion already done above, no need to repeat

      // Upload selected files if asset upload is enabled
      let uploadedArtifacts: PendingArtifact[] = [];
      if (isUploadAssetEnabled && tabState.selectedFiles && tabState.selectedFiles.length > 0) {
        try {
          uploadedArtifacts = await uploadSelectedFiles(tabState.selectedFiles, pendingClientRefId);

          // Store uploaded artifacts in Redux for future reference
          if (uploadedArtifacts.length > 0) {
            dispatch(addPendingArtifacts({ tabId: tabId, artifacts: uploadedArtifacts }));
          }
        } catch (error) {
          console.error("ChatScreen - Error uploading files:", error);
          // Continue with job submission even if file upload fails
        }
      }

      // Combine existing pending artifacts with newly uploaded artifacts
      const allArtifacts = [...pendingArtifacts, ...uploadedArtifacts];

      // Prepare finalize_artifact_upload if there are artifacts and asset upload is enabled
      let finalizeArtifactUpload = undefined;
      if (isUploadAssetEnabled && allArtifacts.length > 0) {
        finalizeArtifactUpload = {
          artifacts: allArtifacts.map(artifact => ({
            artifact_id: artifact.artifact_id,
            entity_id: artifact.entity_id,
            entity_type: artifact.entity_type,
            visibility: artifact.visibility,
            file_name: artifact.file_name,
            description: artifact.description,
            file_path: artifact.file_path
          }))
        };

      }

      // Submit the job
      const response = await agentApi.submitCloudJob(payload, pendingClientRefId, false, finalizeArtifactUpload);

      // Clear pending artifacts and selected files/images after successful submission
      if (isUploadAssetEnabled && allArtifacts.length > 0) {
        dispatch(clearPendingArtifacts({ tabId: tabId }));
      }

      // Clear both selectedFiles and selectedImages from tab state after successful submission
      const needsClearing = (isUploadAssetEnabled && tabState.selectedFiles?.length > 0) ||
                           (tabState.selectedImages?.length > 0);

      if (needsClearing) {
        updateTabState(tabId, {
          ...tabState,
          selectedFiles: [],
          selectedImages: []
        });
      }

      // Update tab state with successful submission
      const updatedTabState = {
        ...tabState,
        containerId: pendingClientRefId,
        client_ref_id: pendingClientRefId,
        apiCallInProgress: false,
        needsApiCall: false,
        task:task,
        fromJobList: true,
        jobId: pendingClientRefId,
        apiCallSuccessful: true,
        pending_client_ref_id: undefined,
        fromShowCase: false,
        showCase:false,
        created_by: user?.id,
      };

      updateTabState(tabId, updatedTabState);

    } catch (error :any) {
      console.error("Error submitting job:", error);
      const errorMessage = error?.response?.data?.detail || error?.message;

      toast({
        title: "Error",
        description: errorMessage || "Failed to submit task. Please try again.",
        variant: "destructive",
        duration: 5000,
      });

      setTabs(prevTabs =>
        prevTabs.filter(tab => tab.id !== tabId)
      );
      
      setActiveTab('home');
    } finally {
      submissionInProgressRef.current = false;
    }
  }, [tabState, tabId, updateTabState, refetch, toast, setTabs, setActiveTab, isUploadAssetEnabled]);

  const checkPendingSubmissionRef = useRef(true);


  const hasOpenedPreviewBefore = () => {
    return localStorage.getItem(`hasOpenedPreviewBefore-${jobId}`) === 'true';
  }

  const handleOpenQrCodeModal = () => {
    setIsQrCodeModalOpen(true);
  }

  useEffect(() => {
    const checkPendingSubmission = async () => {
      if (tabState.pending_client_ref_id && !tabState.client_ref_id && tabState.apiCallInProgress) {
        console.log("Detected page refresh during API call, checking job status...");

        try {
            updateTabState(tabId, {
              ...tabState,
              task: tabState.task,
              clientRefId: tabState.pending_client_ref_id,
              apiCallInProgress: false,
              needsApiCall: false,
              pending_client_ref_id: undefined,
              jobId: tabState.pending_client_ref_id,
              fromShowCase: false,
              showCase:false,
              created_by: user?.id,
            });
            
          } catch (error) {
          console.log("Job not found or API call failed, resetting state for retry");
          // Reset state to allow retry
          updateTabState(tabId, {
            ...tabState,
            apiCallInProgress: false,
            needsApiCall: true,
          });
        }
      }
    };

    if (tabState.pending_client_ref_id && !tabState.client_ref_id && tabState.apiCallInProgress) {
      
      if(checkPendingSubmissionRef.current){
        checkPendingSubmission();
        checkPendingSubmissionRef.current = false;
      }
    }
  }, []);

  useEffect(() => {
    if (tabState.needsApiCall && !tabState.apiCallInProgress && !submissionInProgressRef.current) {
      // Additional safety check to ensure we have the required data
      if (tabState.task && (tabState.image || tabState.selectedImage)) {
        console.log("ChatScreen - Triggering submitJOB with small delay to ensure state propagation");
        // Small delay to ensure tab state is fully propagated from EmbeddedTask
        setTimeout(() => {
          submitJOB();
        }, 50);
      } else {
        console.error("ChatScreen - Cannot submit job, missing required data:", {
          task: tabState.task,
          image: tabState.image,
          selectedImage: tabState.selectedImage,
          tabState
        });

        // Retry after a short delay in case the state is still propagating
        setTimeout(() => {
          const currentTabState = getTabState(tabId);
          console.log("ChatScreen - Retrying after delay, current tab state:", currentTabState);

          if (currentTabState.task && (currentTabState.image || currentTabState.selectedImage)) {
            console.log("ChatScreen - Retry successful, triggering submitJOB");
            submitJOB();
          } else {
            console.error("ChatScreen - Retry failed, still missing required data");
          }
        }, 200);
      }
    }
  }, [tabState.needsApiCall, tabState.apiCallInProgress, tabState.task, tabState.image, tabState.selectedImage]);

  // Use AlertContext to get banner height directly
  const { bannerHeight } = useAlert();
  const heightDiff = bannerHeight + 56;

  const handleForkSubmission = async ( jobId: string, summaryData: string )=>{
    
    try {
      
      const response = await agentApi.submitFork(jobId, summaryData);
      if (response.status === 200 || response.status === 201) {
        modalOpen && setModalOpen && setModalOpen({
          ...modalOpen,
          fork: false,
        });
      }
      console.log("Fork API Response:", response);
    } catch (error) {
      console.error("Error submitting fork:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fork your task, please try again.",
        variant: "destructive",
      });
      modalOpen && setModalOpen && setModalOpen({
        ...modalOpen,
        fork: false,
      });
    }
  }

  if (showSetupLoader) {
    return (
      <ErrorBoundary>
        <div className="w-screen h-full">
          <TaskSetupLoader
            complete={setupLoaderComplete}
            onComplete={handleSetupLoaderComplete}
          />
        </div>
      </ErrorBoundary>
    );
  }

  if (isWakingUp) {
    return (
      <ErrorBoundary>
        <div className="flex flex-col items-center justify-center max-w-lg h-[100dvh] px-4 mx-auto">
          <WakeupProgress
            isVisible={true}
            onComplete={completeWakeup}
            apiCallComplete={apiCallComplete}
          />
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <ResizablePanelGroup
        direction="horizontal"
        className="relative flex w-full bg-[#0F0F10]"
        style={{ height: `calc(100dvh - ${heightDiff}px)` }}
      >
        {/* Main Content Panel */}
        <ResizablePanel
          defaultSize={
            isMobile ? 100 : isAnyPanelOpen ? 100 - getPanelDefaultSize() : 100
          }
          minSize={isMobile ? 100 : 45}
          maxSize={isMobile ? 100 : undefined}
          className={`${
            isAnyPanelOpen && !isMobile ? "md:block hidden" : "block"
          }`}
        >
          <div className="relative flex flex-col h-full">
            <div className="h-10 md:h-16 pt-1 md:pt-3 bg-transparent md:bg-gradient-to-b from-[#0f0f0f] to-[#0f0f0f00] w-fit md:w-full right-0 md:right-3 fixed md:absolute top-[14px] md:top-[0px] z-[50] md:z-[49]">
              <ChatHeader
                handleVSCodeLink={handleVSCodeLink}
                handleShowDiff={handleShowDiff}
                handlePreviewClick={handlePreviewClick}
                handleInfoClick={() => handleInfoPanelVisibility(true)}
                handleDeployClick={handleDeployClick}
                handlePublishClick={handlePublishClick}
                showActions={
                  !showLogsPanel &&
                  !showSubagentPanel &&
                  !showInfoPanel &&
                  !showUrlPreviewPanel &&
                  !showDeployPanel
                }
                hideImportantActions={hideImportantActions && !isEmergentUser}
                jobId={jobId}
                podIsPaused={podIsPaused}
                enableDeploy={
                  bulkWriteExists &&
                  virtuosoData[virtuosoData.length - 1]?.fork_status !=
                    "running"
                }
                publishConfig={{
                  enablePublish: isExpoImage,
                  isPublishing: isPublishing,
                  isPublished: jobDetails?.isPublished
                }}
                previewUrl={previewUrl}
                buildMode={buildMode}
              />
            </div>

            <div className="relative flex-1 w-full h-full overflow-hidden">
              <div className="absolute inset-0">
                {(!jobDetails?.forked?.parent_job_id &&
                  aiMessages.length === 0) ||
                (jobDetails?.forked?.parent_job_id &&
                  aiMessages.length <= 1) ? (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <div className="max-w-md">
                      <p className="text-[#C4C4CC] text-base mb-2">
                        <Spinner />
                      </p>
                    </div>
                  </div>
                ) : (
                  <Virtuoso
                    ref={virtuosoRef}
                    className="w-full h-full overflow-x-hidden"
                    style={{ height: "100%" }}
                    data={virtuosoData}
                    atBottomStateChange={(atBottom) => {
                      checkIfReallyAtBottom(atBottom);
                    }}
                    followOutput={
                      isAtBottom && !userScrolledRef.current ? "smooth" : false
                    }
                    itemContent={(index: number) => {
                      const message = aiMessages[index] as any;
                      const acc_cost = budgetInfo?.current_usage || 0;
                      const max_budget = budgetInfo?.max_budget || 0;
                      const handleAddToken = (
                        variant: "increase_budget" | "add_credits"
                      ) => {
                        setAddTokenVariant(variant);
                        setAddTokenTriggeredLocation(
                          variant !== "increase_budget"
                            ? "add_credits"
                            : "exit_cost"
                        );
                        setIsAddTokenModalOpen(true);
                      };

                      // console.log("ChatScreen - Rendering message:", aiMessages);

                      const onShowSubagentMessages = (
                        messages: any,
                        stepNum: any
                      ) => {
                        if (messages && messages.length > 0) {
                          setSelectedSubagentMessages(messages);
                          setSelectedSubagent(messages[0].expertise_type);
                          setSelectedStepNum(stepNum);
                          setSelectedMessageId(aiMessages[index].id);
                          togglePanel({ panelName: "showSubagentPanel" });
                        }
                      };

                      const handleRollback = () => {
                        if (agentState?.agent_running) {
                          showErrorToast(
                            "Agent is running",
                            "Please wait for the agent to finish or stop running before rolling back"
                          );
                          return;
                        }

                        if (aiMessages[index].request_id) {
                          setRollbackRequestId(aiMessages[index].request_id);
                        }
                        setIsRollbackModalOpen(true);
                      };

                      const contextChunk = aiMessages.filter(
                        (message) => message.warning_for_token_limit === true
                      );
                      const current_token =
                        contextChunk[contextChunk.length - 1]
                          ?.current_token_count || 0;
                      const max_token =
                        contextChunk[contextChunk.length - 1]
                          ?.max_token_count || 200000;

                      const assistantMessages = aiMessages.filter(
                        (msg) => msg.role === "assistant"
                      );

                      const lastAssistantMessage =
                        assistantMessages[assistantMessages.length - 1];

                      const lastUserMessage =
                        aiMessages[aiMessages.length - 1].role === "user";
                      const errorMessageExists =
                        lastAssistantMessage?.error_message &&
                        typeof lastAssistantMessage.error_message ===
                          "string" &&
                        lastAssistantMessage.error_message.includes(
                          "context limit"
                        );

                      const isRollbackMessage =
                        aiMessages[index].function_name === "rollback";

                      const contextLimitReached =
                        aiMessages.some(
                          (message) => message.warning_for_token_limit === true
                        ) || errorMessageExists;

                      // Ensure token count doesn't exceed max token count for percentage calculation
                      const safeCurrentToken = Math.min(
                        current_token,
                        max_token - 1000
                      );

                      const contextLimitReachedMessage: any = {
                        content: !errorMessageExists
                          ? `The agent is approaching its context limit. To keep the conversation going and avoid losing progress, please summarize the session and save your work.`
                          : `The agent is reached its context limit. To keep the conversation going and avoid losing progress, please summarize the session and save your work.`,
                        containerId: containerId,
                        id: `context_limit_reached-${index}`,
                        role: "assistant",
                        error: false,
                        error_message: errorMessageExists
                          ? `You have reached the context limit for this chat! To continue working on this project, please use Rollback and Clear Messages feature to an earlier message or Push to Github and Start a new run.`
                          : "",
                        function_name: "context_limit_reached",
                        job_id: jobId,
                        step_number: 0,
                        current_token_count: errorMessageExists
                          ? max_token
                          : safeCurrentToken,
                        max_token_count: max_token,
                        container_id: containerId,
                        timestamp: message.timestamp,
                        action: "context_limit_reached",
                        error_message_exists: errorMessageExists,
                      };

                      // Check if this is the context limit reached message
                      const isContextLimitMessage =
                        index === aiMessages.length - 1 &&
                        contextLimitReached &&
                        !lastUserMessage;

                      if (message.function_name == "forked") {
                        return (
                          <ForkBar
                            state={
                              message.forked_job_id &&
                              message.fork_status != "failed"
                                ? "completed"
                                : message.fork_status == "failed"
                                ? "error"
                                : "running"
                            }
                            count={index + 1}
                            message={message}
                            disabled={podIsPaused || credits <= 0}
                            isDropdownOpen={isDropdownOpen}
                            setIsDropdownOpen={setIsDropdownOpen}
                            currentJobId={jobDetails?.job_id}
                            messages={aiMessages}
                            onOpenForkModal={handleOpenForkModal}
                            onOpenForkModalWithSummary={
                              handleOpenForkModalWithSummary
                            }
                            isLastMessage={index === aiMessages.length - 1}
                          />
                        );
                      }

                      return (
                        <div className="flex justify-center w-full min-h-[1px]">
                          <div
                            className={cn(
                              "w-full hover:bg-[rgba(255,255,255,0.02)]",
                              isRollbackMessage &&
                                "bg-[#FFAA800D] hover:bg-[#FFAA800D] mb-2",
                              index == 0 && "mt-10"
                            )}
                          >
                            {aiMessages.length > 0 &&
                              index == 0 &&
                              jobDetails?.forked?.parent_job_id && (
                                <ForkBar
                                  isDropdownOpen={isDropdownOpen}
                                  setIsDropdownOpen={setIsDropdownOpen}
                                  state={"forked"}
                                  message={aiMessages[aiMessages.length - 1]}
                                  forked={jobDetails?.forked}
                                  currentJobId={jobDetails?.job_id}
                                  messages={aiMessages}
                                  onOpenForkModal={handleOpenForkModal}
                                  onOpenForkModalWithSummary={
                                    handleOpenForkModalWithSummary
                                  }
                                  disabled={podIsPaused || credits <= 0}
                                  isLastMessage={false}
                                />
                              )}
                            <div className={cn("mx-auto px-4 w-full")}>
                              {
                                // @ts-ignore
                                !message.switching_to_build_mode &&
                                  message.role === "assistant" &&
                                  "action" in message &&
                                  !(
                                    aiMessages.length - 1 === index &&
                                    contextLimitReached &&
                                    message.action === "pause"
                                  ) && (
                                    <AgentMessageItem
                                      lastGithubUsed={targetRepo}
                                      togglePanel={togglePanel}
                                      message={message as any}
                                      userInitials={userInitials}
                                      onMergeToLocal={() => {}}
                                      onShowSubagentMessages={
                                        onShowSubagentMessages
                                      }
                                      onSubagentClick={handleSubagentClick}
                                      isSubagentActive={isSubagentActive}
                                      handleOpenVsCode={handleOpenVSCodeUrl}
                                      handleRollback={handleRollback}
                                      hideImportantActions={
                                        hideImportantActions
                                      }
                                      isCloudFlow={isCloudFlow}
                                      isSubagent={false}
                                      handleAddToken={handleAddToken}
                                      acc_cost={acc_cost}
                                      max_budget={max_budget}
                                      selectedMessageId={selectedMessageId}
                                      currentChunk={currentChunk}
                                      forkStatus={
                                        virtuosoData[virtuosoData.length - 1]
                                          ?.fork_status
                                      }
                                      isLastMessage={
                                        index === aiMessages.length - 1
                                      }
                                      nextMessageExists={
                                        index + 1 <= aiMessages.length - 1
                                      }
                                      agentState={agentState}
                                      panelState={panelState}
                                      jobDetails={jobDetails}
                                      agentName={tabState?.agentName}
                                      onPause={() => {
                                        handlePauseAgent({
                                          origin: "MainInput",
                                        });
                                      }}
                                      isPauseLoading={isPauseLoading}
                                      podIsPaused={podIsPaused}
                                      modalOpen={modalOpen}
                                      setModalOpen={setModalOpen}
                                    />
                                  )
                              }

                              {message.role === "user" &&
                                !(
                                  index == 0 && jobDetails.forked?.parent_job_id
                                ) && (
                                  <MessageItem
                                    message={aiMessages[index] as any}
                                    hideImportantActions={hideImportantActions}
                                    isCloudFlow={isCloudFlow}
                                    handleRollback={handleRollback}
                                    podIsPaused={podIsPaused}
                                  />
                                )}

                              {isContextLimitMessage && (
                                //@ts-ignore
                                <AgentMessageItem
                                  currentChunk={currentChunk}
                                  togglePanel={togglePanel}
                                  userInitials={userInitials}
                                  hideImportantActions={hideImportantActions}
                                  isCloudFlow={isCloudFlow}
                                  message={contextLimitReachedMessage as any}
                                  selectedMessageId={selectedMessageId}
                                  jobDetails={jobDetails}
                                  agentName={tabState?.agentName}
                                  onPause={() => setPauseWasClicked(true)}
                                  modalOpen={modalOpen}
                                  setModalOpen={setModalOpen}
                                />
                              )}

                              {agentState?.agent_running &&
                                index === aiMessages.length - 1 &&
                                footerComponent()}
                            </div>
                          </div>
                        </div>
                      );
                    }}
                    initialTopMostItemIndex={aiMessages.length - 1}
                    defaultItemHeight={100}
                    overscan={15}
                    onScroll={() => {
                      // When user actively scrolls, update the scroll activity
                      if (scrollEndedTimeoutRef.current) {
                        clearTimeout(scrollEndedTimeoutRef.current);
                      }

                      // Set a timeout to detect when scrolling has ended
                      scrollEndedTimeoutRef.current = setTimeout(() => {
                        // If they're at the bottom after scrolling ends, reset the user scrolled flag
                        if (lastBottomStateRef.current) {
                          userScrolledRef.current = false;
                        }
                        scrollEndedTimeoutRef.current = null;
                      }, 200);
                    }}
                    components={{
                      Footer: () => (
                        <>
                          <div
                            className={cn(isMobile ? "h-[20rem]" : "h-[14rem]")}
                          ></div>
                        </>
                      ),
                    }}
                  />
                )}
              </div>
            </div>
            {(!hideImportantActions || isEmergentUser) &&
              !podIsPaused &&
              !isWakingUp &&
              isInputActive &&
              tabState?.showCase !== true && (
                <div className="absolute z-[49] bottom-0 left-0 right-0 px-0 max-xs:px-0 max-md:px-0 md:px-4 flex flex-col justify-center bg-transparent rounded-t-l-[14px] rounded-t-r-[14px]">
                  {showScrollButton && !showPreviewPrompt && (
                    <div className="flex justify-center mb-2">
                      <button
                        type="button"
                        onClick={scrollToBottom}
                        className="z-[9999] p-2 w-[36px] h-[36px] flex items-center justify-center text-black transition-colors bg-white rounded-full shadow-lg hover:bg-white/80"
                        aria-label="Scroll to bottom"
                      >
                        <img
                          src={SendIcon}
                          alt="Scroll to bottom"
                          className="w-6 h-6 rotate-180"
                        />
                      </button>
                    </div>
                  )}

                  {isMobilePreviewEnabled() && (
                    <PreviewPrompt
                      isVisible={showPreviewPrompt}
                      previewUrl={previewUrl}
                      onPreviewClick={() => {
                        localStorage.setItem(
                          `hasOpenedPreviewBefore-${jobId}`,
                          "true"
                        );
                        togglePanel({
                          panelName: "showUrlPreviewPanel",
                          value: true,
                        });
                      }}
                      ariaLabel="Open preview panel"
                      hasOpenedBefore={hasOpenedPreviewBefore}
                    />
                  )}
                  <div className="relative flex items-center justify-center w-full md:pb-2 pb-0 md:backdrop-blur-md backdrop-blur-md rounded-t-l-[14px] rounded-t-r-[14px]">
                    <ChatInput
                      lastGithubUsed={targetRepo}
                      isDisabled={
                        rollbackStatus === "FAILED" || isInputDisabled()
                      }
                      forked_status={
                        virtuosoData[virtuosoData.length - 1]?.fork_status ||
                        null
                      }
                      onSubmit={handleSubmit}
                      agentState={agentState}
                      containerId={containerId}
                      jobDetails={jobDetails}
                      acc_cost={budgetInfo?.current_usage}
                      max_budget={budgetInfo?.max_budget}
                      onAddToken={async () => {
                        setAddTokenVariant("increase_budget");
                        setIsAddTokenModalOpen(true);
                      }}
                      inputValue={failedPendingValue || ""}
                      isCloudFlow={isCloudFlow}
                      clientRefId={clientRefId}
                      modelName={modelName}
                      modalOpen={modalOpen}
                      setModalOpen={setModalOpen}
                      isSubagentActive={isSubagentActive}
                      disableWhenSubagentActive={true}
                      showFinishSubagentText={true}
                      onOpenSubagentPanel={() => {
                        if (aiMessages.length > 0) {
                          const lastMessage = aiMessages[aiMessages.length - 1];
                          handleSubagentClick(lastMessage);
                        }
                      }}
                      onGitHubPush={(repoDetails) => {
                        // Only update the target repo state, not the source repo
                        setTargetRepo(repoDetails);
                        // Do not update githubDetails (source repo) when pushing to GitHub
                      }}
                      pauseWasClicked={pauseWasClicked}
                      onPause={handlePauseAgent}
                      isPauseLoading={isPauseLoading}
                      subagentName={
                        aiMessages.length > 0
                          ? aiMessages[aiMessages.length - 1].function_name
                          : undefined
                      }
                      buildMode={buildMode}
                      onOpenUploadAssetsModal={handleOpenUploadAssetsModal}
                    />
                  </div>
                </div>
              )}

            {/* Agent Sleep Notification */}
            <AgentSleepNotification
              podIsPaused={podIsPaused}
              archivedPod={archivedPod}
              isInputActive={isInputActive}
              showCase={tabState?.showCase || false}
              githubDetails={githubDetails}
              createdBefore25April={createdBefore25April}
              resumePod={resumePod}
              isWakingUp={isWakingUp}
              jobId={jobId}
            />
          </div>
        </ResizablePanel>

        {/* Animated Resizable Handle - Hidden on mobile */}
        <AnimatedResizableHandle
          isVisible={isAnyPanelOpen}
          withHandle
          className="bg-[#242424] hidden md:flex"
          onDragging={(isDragging) => setIsResizing(isDragging)}
        />

        {/* Animated Resizable Panel */}
        <AnimatedResizablePanel
          isVisible={isAnyPanelOpen}
          isDragging={!isMobile && isResizing}
          defaultSize={isMobile ? 0 : getPanelDefaultSize()}
          minSize={isMobile ? 0 : 30}
          maxSize={isMobile ? 0 : 60}
          className={`bg-[#0F0F10] ${
            isAnyPanelOpen
              ? isMobile
                ? "absolute inset-0 w-full z-50"
                : "md:relative absolute inset-0 w-full"
              : ""
          }`}
          exitDelay={400}
        >
          {/* Animated Panel Content */}
          <motion.div
            className="relative w-full h-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: isResizing ? 0 : 0.2, // Disable animation during resize
            }}
          >
            {/* SubagentMessagesPanel */}
            <motion.div
              className="absolute inset-0"
              initial={{ x: "100%", opacity: 0 }}
              animate={{
                x: showSubagentPanel ? 0 : "100%",
                opacity: showSubagentPanel ? 1 : 0,
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                mass: 0.8,
                duration: isResizing ? 0 : undefined, // Disable during resize
              }}
              style={{
                pointerEvents: showSubagentPanel ? "auto" : "none",
              }}
            >
              {showSubagentPanel && (
                <SubagentMessagesPanel
                  searchState={{ isActive: false, query: "" }}
                  togglePanel={togglePanel}
                  userInitials={userInitials}
                  searchHighlights={[
                    {
                      startIndex: 10,
                      endIndex: 10,
                    },
                  ]}
                  isMatchingMessage={false}
                  key={selectedMessageId || selectedStepNum}
                  isOpen={showSubagentPanel}
                  onClose={() => {
                    togglePanel({
                      panelName: "showSubagentPanel",
                      value: false,
                    });

                    // Track subagent panel visibility change
                    trackFeatureUsage("subagent_panel_toggle", {
                      jobId,
                      containerId,
                      isVisible: false,
                      tabId,
                    });
                  }}
                  messages={selectedSubagentMessages}
                  onSubmit={handleSubagentSubmit}
                  agentState={agentState}
                  containerId={containerId}
                  jobDetails={jobDetails}
                  selectedAgent={selectedSubagent}
                  subagentName={selectedSubagent}
                  isSubagentActive={isSubagentActive}
                  isCloudFlow={isCloudFlow}
                  pauseWasClicked={pauseWasClicked}
                  onPause={handlePauseAgent}
                  isPauseLoading={isPauseLoading}
                  currentChunk={currentChunk}
                />
              )}
            </motion.div>

            {/* Chat Info Panel */}
            <motion.div
              className="absolute inset-0"
              initial={{ x: "100%", opacity: 0 }}
              animate={{
                x: showInfoPanel ? 0 : "100%",
                opacity: showInfoPanel ? 1 : 0,
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                mass: 0.8,
                duration: isResizing ? 0 : undefined,
              }}
              style={{
                pointerEvents: showInfoPanel ? "auto" : "none",
              }}
            >
              {showInfoPanel && (
                <ChatInfoPanel
                  isOpen={showInfoPanel}
                  onClose={() => handleInfoPanelVisibility(false)}
                  onOpenUploadAssetsModal={handleOpenUploadAssetsModal}
                  chatInfo={{
                    modelName: tabState?.modelName || jobDetails.modelName,
                    agentName: tabState?.agentName || "MVP Agent",
                    containerStatus: agentState?.job_running
                      ? "Running"
                      : "Stopped",
                    containerName:
                      containerId?.split("-")[0] === "task"
                        ? containerId
                        : undefined,
                    containerId: containerId,
                    assignedPorts: tabState?.portMapping,
                    jobId: jobId,
                    isCloudFlow: isCloudFlow,
                    promptName: tabState?.promptName,
                    promptVersion: tabState?.promptVersion,
                    costLimit: tabState?.costLimit,
                    vscodeUrl: vscodeUrl,
                    vscodePassword: vscodePassword,
                    targetRepo: targetRepo,
                    sourceRepo: githubDetails,
                  }}
                />
              )}
            </motion.div>

            {/* URL Preview Panel */}
            <motion.div
              className="absolute inset-0"
              initial={{ x: "100%", opacity: 0 }}
              animate={{
                x: showUrlPreviewPanel ? 0 : "100%",
                opacity: showUrlPreviewPanel ? 1 : 0,
              }}
              transition={
                isResizing
                  ? { duration: 0, ease: "linear" }
                  : {
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                      mass: 0.8,
                    }
              }
              style={{
                pointerEvents: showUrlPreviewPanel ? "auto" : "none",
              }}
            >
              {showUrlPreviewPanel && (
                <div
                  className="w-full h-full"
                  style={{
                    pointerEvents: isResizing ? "none" : "auto",
                  }}
                >
                  <UrlPreviewPanel
                    shareableLink={shareableUrl}
                    isOpen={showUrlPreviewPanel}
                    onClose={() =>
                      togglePanel({
                        panelName: "showUrlPreviewPanel",
                        value: false,
                      })
                    }
                    showCase={tabState?.showCase || false}
                    previewUrl={tabState?.previewUrl || previewUrl}
                    podIsPaused={podIsPaused}
                    onResumePod={resumePod}
                    isFromGithub={githubDetails && createdBefore25April}
                    shouldReloadIframe={shouldReloadIframe}
                    onIframeReloaded={handleIframeReloaded}
                    isResizing={isResizing}
                    archivedPod={archivedPod}
                    jobId={jobId}
                    env_image={jobDetails?.env_image}
                    handleOpenQrCodeModal={handleOpenQrCodeModal}
                  />
                </div>
              )}
            </motion.div>

            {/* Deploy Panel */}
            <motion.div
              className="absolute inset-0"
              initial={{ x: "100%", opacity: 0 }}
              animate={{
                x: showDeployPanel ? 0 : "100%",
                opacity: showDeployPanel ? 1 : 0,
              }}
              transition={
                isResizing
                  ? { duration: 0, ease: "linear" }
                  : {
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                      mass: 0.8,
                    }
              }
              style={{
                pointerEvents: showDeployPanel ? "auto" : "none",
              }}
            >
              {showDeployPanel && (
                <DeployPanel
                  isOpen={showDeployPanel}
                  onClose={() =>
                    togglePanel({ panelName: "showDeployPanel", value: false })
                  }
                  jobId={jobId}
                  podIsPaused={podIsPaused}
                  agentStatus={agentState?.job_running ? "running" : "stopped"}
                  handleJobClick={handleJobClick}
                  onShareLogs={onShareLogs}
                />
              )}
            </motion.div>
          </motion.div>
        </AnimatedResizablePanel>

        {modalOpen.fork && (
          <ForkModal
            isOpen={modalOpen.fork}
            appName={jobDetails?.dynamic_preview_subdomain || ""}
            onOpenChange={() => {
              setModalOpen((prev) => {
                return {
                  ...prev,
                  fork: !prev.fork,
                };
              });
              // Clear the fork modal data when closing
              setForkModalData(null);
            }}
            onFork={handleForkSubmission}
            podIsPaused={podIsPaused}
            jobId={forkModalData?.jobId || jobDetails?.job_id}
            messages={forkModalData?.messages || aiMessages}
            summaryData={forkModalData?.summaryData}
            startInSummaryMode={forkModalData?.startInSummaryMode}
          />
        )}

        {/* Upload Assets Modal */}
        <UploadAssetsModal
          isOpen={showUploadAssetsModal}
          onOpenChange={(open) => {
            setShowUploadAssetsModal(open);
            // Clear initial files when modal closes
            if (!open) {
              setUploadAssetsInitialFiles([]);
            }
          }}
          jobId={jobDetails?.job_id}
          handleSendMessage={handleSendMessage}
          initialFiles={uploadAssetsInitialFiles}
          onUploadComplete={(artifactId: string, fileName: string) => {
            console.log(`Upload completed: ${fileName} (${artifactId})`);
            // You can add additional logic here if needed
          }}
          onUploadError={(fileName: string, error: string) => {
            console.error(`Upload failed for ${fileName}:`, error);
            // You can add additional error handling here if needed
          }}
        />
      </ResizablePanelGroup>

      <AnimatePresence>
        {isRollbackModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            <RollbackModal
              isOpen={isRollbackModalOpen}
              onOpenChange={(modalStatus) => {
                setIsRollbackModalOpen(modalStatus);
              }}
              rollbackRequestId={rollbackRequestId}
              containerId={containerId}
              jobId={jobId}
              isCloudFlow={isCloudFlow}
              session={session}
              resetPolling={resetPolling}
              setAiMessages={setAiMessages}
              // agentState={agentState}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showVSCodeDialog && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            <VSCodeLinkModal
              isOpen={showVSCodeDialog}
              onOpenChange={setShowVSCodeDialog}
              vscodeUrl={vscodeUrl}
              vscodePassword={vscodePassword}
              onOpenInBrowser={handleOpenVSCodeUrl}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isQrCodeModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            <QRCodeModal
              isOpen={isQrCodeModalOpen}
              onOpenChange={setIsQrCodeModalOpen}
              previewUrl={previewUrl}
            />
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {showDiffModal && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            <DiffModal
              isOpen={showDiffModal}
              onOpenVSCode={handleOpenVSCodeUrl}
              onClose={() => setShowDiffModal(false)}
              containerId={containerId}
              modifiedFiles={modifiedFiles}
              initialCommitId={initialCommitId}
              isCloudFlow={isCloudFlow}
              jobId={jobId}
            />
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {isQrCodeModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            {/* <DiffModal
              isOpen={showDiffModal}
              onOpenVSCode={handleOpenVSCodeUrl}
              onClose={() => setShowDiffModal(false)}
              containerId={containerId}
              modifiedFiles={modifiedFiles}
              initialCommitId={initialCommitId}
              isCloudFlow={isCloudFlow}
              jobId={jobId}
            /> */}

            
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isAddTokenModalOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 5 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 0.8,
            }}
          >
            <AddTokenModal
              modelName={modelName}
              triggeredLocation={addTokenTriggeredLocation}
              variant={addTokenVariant}
              handleSendMessage={handleSendMessage}
              isOpen={isAddTokenModalOpen}
              onOpenChange={setIsAddTokenModalOpen}
              currentCost={budgetInfo?.current_usage || 0}
              maxBudget={budgetInfo?.max_budget}
              jobId={jobId}
              onBudgetUpdate={async () => {
                fetchBudgetInfo();
                setIsAddTokenModalOpen(false);
                trackFeatureUsage("budget_updated", {
                  jobId,
                  containerId,
                  tabId,
                  newMaxBudget: budgetInfo?.max_budget || 0,
                  currentUsage: budgetInfo?.current_usage || 0,
                });
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </ErrorBoundary>
  );
}