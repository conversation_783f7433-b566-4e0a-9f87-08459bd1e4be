import React, { useEffect, useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import CopyButton from "../CopyButton";
import QRCode from "qrcode";
import AexpoLogo from "../icons/AexpoLogo";
import ExternalLinkIcon from "../icons/ExternalLinkIcon";

interface QRCodeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  previewUrl: string;
}

export const QRCodeModal: React.FC<QRCodeModalProps> = ({
  isOpen,
  onOpenChange,
  previewUrl,
}) => {
  const { isMobile } = useScreenSize();
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [expoQrCodeDataUrl, setExpoQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingExpo, setIsGeneratingExpo] = useState(false);
  const [expoSectionOpen, setExpoSectionOpen] = useState(false);
  const [previewSectionOpen, setPreviewSectionOpen] = useState(true);

  // Handle exclusive opening of sections - ensure exactly one is always open
  const handleExpoSectionToggle = (open: boolean) => {
    // If trying to close this section, only allow if the other section will be open
    if (!open && !previewSectionOpen) {
      // Don't allow closing if the other section is also closed
      return;
    }

    setExpoSectionOpen(open);
    if (open) {
      setPreviewSectionOpen(false);
    } else {
      // If closing this section, ensure the other one opens
      setPreviewSectionOpen(true);
    }
  };

  const handlePreviewSectionToggle = (open: boolean) => {
    // If trying to close this section, only allow if the other section will be open
    if (!open && !expoSectionOpen) {
      // Don't allow closing if the other section is also closed
      return;
    }

    setPreviewSectionOpen(open);
    if (open) {
      setExpoSectionOpen(false);
    } else {
      // If closing this section, ensure the other one opens
      setExpoSectionOpen(true);
    }
  };

  // Convert https:// URL to exp:// URL for Expo
  const uuid = previewUrl.replace(/^https:\/\//, "").split(".")[0];
  const expUrl = `exp://${uuid}.ngrok.io`;

  // Expo Go download URL
  const expoGoUrl = "https://expo.dev/client";

  useEffect(() => {
    if (isOpen) {
      // Generate QR code for app preview
      if (expUrl) {
        setIsGenerating(true);
        QRCode.toDataURL(expUrl, {
          width: 256,
          margin: 2,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
        })
          .then((dataUrl: any) => {
            setQrCodeDataUrl(dataUrl);
          })
          .catch((error: any) => {
            console.error("Error generating app preview QR code:", error);
          })
          .finally(() => {
            setIsGenerating(false);
          });
      }

      // Generate QR code for Expo Go download
      setIsGeneratingExpo(true);
      QRCode.toDataURL(expoGoUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      })
        .then((dataUrl: any) => {
          setExpoQrCodeDataUrl(dataUrl);
        })
        .catch((error: any) => {
          console.error("Error generating Expo Go QR code:", error);
        })
        .finally(() => {
          setIsGeneratingExpo(false);
        });
    }
  }, [isOpen, expUrl, expoGoUrl]);

  // Shared content component
  const renderContent = ({
    showHeader = false,
  }: { showHeader?: boolean } = {}) => (
    <div className="relative space-y-6">
      {showHeader && (
        <div className="flex flex-col space-y-2">
          <DialogTitle className="text-xl font-medium text-white">
            Preview on your phone
          </DialogTitle>
          <DialogDescription className="text-sm text-[#9CA3AF]">
            Download Expo Go app to see your app in your phone or just directly
            scan the preview QR to see it in your native browser
          </DialogDescription>
        </div>
      )}

      {/* Accordion Sections */}
      <div className="space-y-5">
        {/* Download Expo Go Section */}
        <Collapsible
          open={expoSectionOpen}
          onOpenChange={handleExpoSectionToggle}
          className="bg-[#202021] rounded-lg overflow-hidden"
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-5 border-none text-left hover:bg-[#202021] transition-colors">
            <h3 className="text-lg font-medium text-white flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M2 15C2 14.4477 2.44772 14 3 14C3.55228 14 4 14.4477 4 15C4 17.2091 5.79087 19 8 19H16C18.2091 19 20 17.2091 20 15C20 14.4477 20.4477 14 21 14C21.5523 14 22 14.4477 22 15C22 18.3137 19.3137 21 16 21H8C4.68629 21 2 18.3137 2 15ZM11 4C11 3.44772 11.4477 3 12 3C12.5523 3 13 3.44772 13 4V12.9844C13.4293 12.5461 13.8312 12.0809 14.2002 11.5889C14.5315 11.1471 15.1578 11.0574 15.5996 11.3887C16.0414 11.72 16.131 12.3463 15.7998 12.7881C15.0103 13.8408 14.0944 14.7917 13.0732 15.6191V15.6201C12.761 15.873 12.3801 16 12 16C11.6199 16 11.239 15.873 10.9268 15.6201V15.6191C10.0333 14.8951 9.22012 14.0772 8.50195 13.1787L8.2002 12.7881L8.14258 12.7031C7.88133 12.269 7.98631 11.6992 8.40039 11.3887C8.81456 11.0782 9.39089 11.1372 9.73438 11.5098L9.7998 11.5889L10.0645 11.9297C10.3583 12.2973 10.6712 12.6487 11 12.9844V4Z"
                  fill="white"
                  fill-opacity="0.8"
                />
              </svg>
              Download Expo Go App
            </h3>
            <svg
              className={`w-5 h-5 text-[#9CA3AF] transition-transform duration-200 ${expoSectionOpen ? "" : "-rotate-90"}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </CollapsibleTrigger>

          <CollapsibleContent className="data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up overflow-hidden">
            <div className="space-y-4 p-5 pt-0">
              <p className="text-sm text-[#9CA3AF]">
                Scan this QR code to install the Expo Go app from the Play Store.
              </p>

              <div className="flex items-center rounded-lg p-4 px-0">
                <div className="flex items-center w-full justify-between space-x-2">
                  {/* QR Code for Expo Go download */}
                  <div className="bg-[#FFFFFF08] rounded-lg justify-center items-center flex flex-1 p-4">
                    {isGeneratingExpo ? (
                      <div className="w-16 h-16 flex items-center justify-center bg-[#FFFFFF08] rounded">
                        <div className="text-gray-500 text-xs">Loading...</div>
                      </div>
                    ) : expoQrCodeDataUrl ? (
                      <img
                        src={expoQrCodeDataUrl}
                        alt="QR Code for Expo Go download"
                        className="w-48 h-48 rounded-lg opacity-80"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-black flex items-center justify-center text-white text-xs">
                        QR Code
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col space-y-4 justify-center items-center h-full rounded-2xl border border-1 border-white/5 p-4">
                    <div className="rounded-[10px] h-full w-full bg-gradient-to-b from-white/[0.02] to-white/0 flex items-center justify-center min-h-[144px]">
                      <AexpoLogo  width={71} height={20} />
                    </div>
                    <a href="https://expo.dev/client" target="_blank" rel="noopener noreferrer">
                    <div className="bg-[#FFFFFF0A] rounded-[10px] flex space-x-2 items-center justify-center py-[10px] px-4">
                      <p className="text-white/80 font-medium text-sm">
                        Download Expo Go
                      </p>
                      <ExternalLinkIcon width={16} height={16} />
                    </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Preview QR Code Section */}
        <Collapsible
          open={previewSectionOpen}
          onOpenChange={handlePreviewSectionToggle}
          className="bg-[#202021] rounded-lg overflow-hidden"
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-5 border-none text-left hover:bg-[#202021] transition-colors">
            <h3 className="text-lg font-medium text-white flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M9 16C9 15.4477 8.5523 15 8 15H6C5.44771 15 5 15.4477 5 16V18C5 18.5523 5.44771 19 6 19H8C8.5523 19 9 18.5523 9 18V16ZM13 20V19.9902C13 19.438 13.4477 18.9902 14 18.9902C14.5523 18.9902 15 19.438 15 19.9902V20C15 20.5523 14.5523 21 14 21C13.4477 21 13 20.5523 13 20ZM17 20V18C17 17.4477 17.4477 17 18 17H20C20.5523 17 21 17.4477 21 18C21 18.5523 20.5523 19 20 19H19V20C19 20.5523 18.5523 21 18 21C17.4477 21 17 20.5523 17 20ZM13 14C13 13.4477 13.4477 13 14 13C14.5523 13 15 13.4477 15 14V15H16C16.5523 15 17 15.4477 17 16C17 16.5523 16.5523 17 16 17H14C13.4477 17 13 16.5523 13 16V14ZM20 13C20.5523 13 21 13.4477 21 14C21 14.5523 20.5523 15 20 15H18C17.4477 15 17 14.5523 17 14C17 13.4477 17.4477 13 18 13H20ZM9 6C9 5.44771 8.55229 5 8 5H6C5.44771 5 5 5.44771 5 6V8C5 8.55229 5.44771 9 6 9H8C8.55229 9 9 8.55229 9 8V6ZM19 6C19 5.44771 18.5523 5 18 5H16C15.4477 5 15 5.44771 15 6V8C15 8.5523 15.4477 9 16 9H18C18.5523 9 19 8.5523 19 8V6ZM11 18C11 19.6569 9.65685 21 8 21H6C4.34315 21 3 19.6569 3 18V16C3 14.3431 4.34315 13 6 13H8C9.65685 13 11 14.3431 11 16V18ZM11 8C11 9.65685 9.65685 11 8 11H6C4.34315 11 3 9.65685 3 8V6C3 4.34315 4.34315 3 6 3H8C9.65685 3 11 4.34315 11 6V8ZM21 8C21 9.65685 19.6569 11 18 11H16C14.3431 11 13 9.65685 13 8V6C13 4.34315 14.3431 3 16 3H18C19.6569 3 21 4.34315 21 6V8Z"
                  fill="white"
                />
              </svg>
              Preview QR Code
            </h3>
            <svg
              className={`w-5 h-5 text-[#9CA3AF] transition-transform duration-200 ${previewSectionOpen ? "rotate-90" : "rotate-none"}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </CollapsibleTrigger>

          <CollapsibleContent className="data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up overflow-hidden">
            <div className="p-5 pt-0 flex flex-col items-start space-y-4">
              {/* Main QR Code Display */}
              <div className="text-start space-y-2 w-full">
                <p className="text-sm text-[#9CA3AF]">
                  Scan this QR code with your camera to preview the app
                </p>
              </div>
              <div className="bg-[#FFFFFF08] p-4 rounded-lg">
                {isGenerating ? (
                  <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                    <div className="text-gray-500 text-sm">
                      Generating QR code...
                    </div>
                  </div>
                ) : qrCodeDataUrl ? (
                  <img
                    src={qrCodeDataUrl}
                    alt="QR Code for app preview"
                    className="w-48 h-48 rounded-lg opacity-80"
                  />
                ) : (
                  <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                    <div className="text-gray-500 text-sm">
                      Failed to generate QR code
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );

  // Shared footer component
  const renderFooter = () => (
    <div className="flex-row justify-end w-full flex gap-4">
      <Button
        className="px-6 py-2 bg-transparent border border-[#404040] text-white hover:bg-[#2A2A2B]"
        variant="outline"
        onClick={() => onOpenChange(false)}
      >
        Close
      </Button>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title=""
        description=""
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={renderFooter()}
      >
        <div className="h-[70dvh] flex flex-col px-4 pt-4 bg-[#18181A]">
          {renderContent({ showHeader: true })}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-xl w-xl bg-[#18181A] border-[#242424] text-white">
        <DialogHeader className="pb-4 bg-[#18181A] space-y-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-[22px] font-medium text-white">
              Preview on your phone
            </DialogTitle>
            <button
              onClick={() => onOpenChange(false)}
              className="text-[#9CA3AF] hover:text-white transition-colors p-1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M18 6L6 18M6 6l12 12" />
              </svg>
            </button>
          </div>
          <DialogDescription className="text-sm text-[#737780]">
            Download Expo Go app to see your app in your phone or just directly
            scan the preview QR to see it in your native browser
          </DialogDescription>
        </DialogHeader>
        <div className="px-6 pb-6 pt-5">{renderContent()}</div>
      </DialogContent>
    </Dialog>
  );
};
